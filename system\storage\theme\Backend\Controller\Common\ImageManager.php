<?php

namespace Theme25\Backend\Controller\Common;

class Imagemanager extends \Theme25\Controller {

    public function __construct($registry) {
        parent::__construct($registry, 'common/imagemanager');
    }

    /**
     * Основен метод за листване на изображения и папки
     */
    public function index() {
        // Инициализиране на данните
        $this->initAdminData();

        // Създаване на суб-контролер за директории
        $subController = $this->setBackendSubController('Common/ImageManager/Directory', $this);

        // Делегиране на работата към суб-контролера
        $result = $subController->loadContents();

        $this->setJSONResponseOutput($result);
    }

    /**
     * Метод за качване на файлове
     */
    public function upload() {
        // Инициализиране на данните
        $this->initAdminData();

        // Създаване на суб-контролер за качване
        $subController = $this->setBackendSubController('Common/ImageManager/Upload', $this);

        // Делегиране на работата към суб-контролера
        $result = $subController->processFiles();

        $this->setJSONResponseOutput($result);
    }

    /**
     * Зареждане на template за мениджъра на изображения
     */
    public function template() {
        // Инициализиране на данните
        $this->initAdminData();

        // Рендиране на template-а за мениджъра на изображения
        $this->renderPartTemplate('common/image_manager');
    }

    /**
     * Търсене на изображения и папки
     */
    public function search() {
        // Инициализиране на данните
        $this->initAdminData();

        // Създаване на суб-контролер за директории
        $subController = $this->setBackendSubController('Common/ImageManager/Directory', $this);

        // Делегиране на работата към суб-контролера
        $result = $subController->searchItems();

        $this->setJSONResponseOutput($result);
    }

    /**
     * Генерира thumbnail за изображение (lazy loading)
     */
    public function thumbnail() {
        ob_start();
        $this->initAdminData();

        // if (!$this->hasPermission('modify', 'common/filemanager')) {
        //     ob_clean();
        //     $this->setJSONResponseOutput([
        //         'success' => false,
        //         'error' => 'Няма права за достъп',
        //         'show_warning_icon' => true
        //     ]);
        //     return;
        // }

        $imagePath = $this->requestGet('image') ?? '';
        $width = (int)($this->requestGet('width') ?? 150);
        $height = (int)($this->requestGet('height') ?? 150);

        if (empty($imagePath)) {
            ob_clean();
            $this->setJSONResponseOutput([
                'success' => false,
                'error' => 'Липсва път до изображение',
                'show_warning_icon' => true
            ]);
            return;
        }

        try {
            $this->loadModelAs('tool/image', 'imageModel');

            $fullImagePath = ThemeData()->getImageServerPath() . $imagePath;
            if (!file_exists($fullImagePath)) {
                ob_clean();
                $this->setJSONResponseOutput([
                    'success' => false,
                    'error' => 'Изображението не съществува',
                    'show_warning_icon' => true
                ]);
                return;
            }

            $thumbnailUrl = $this->imageModel->resize($imagePath, $width, $height);

            if (empty($thumbnailUrl)) {
                ob_clean();
                $this->setJSONResponseOutput([
                    'success' => false,
                    'error' => 'Не може да се генерира миниатюра',
                    'show_warning_icon' => true
                ]);
                return;
            }

            ob_clean();
            $this->setJSONResponseOutput([
                'success' => true,
                'thumbnail' => $thumbnailUrl
            ]);
        } catch (Exception $e) {
            ob_clean();
            $this->setJSONResponseOutput([
                'success' => false,
                'error' => 'Грешка при генериране на thumbnail: ' . $e->getMessage(),
                'show_warning_icon' => true
            ]);
        }
    }
    /**
     * Създаване на нова папка
     */
    public function createfolder() {
        // Проверка за права на достъп
        // if (!$this->user->hasPermission('modify', 'common/filemanager')) {
        //     $this->setJSONResponseOutput([
        //         'success' => false,
        //         'error' => 'Няма права за достъп'
        //     ]);
        //     return;
        // }

        // Получаване на параметрите
        $folderName = trim($this->requestPost('folder_name') ?? '');
        $currentDirectory = trim($this->requestPost('current_directory') ?? '');

        // Валидация на името на папката

        $validation = $this->setBackendSubController('Common/ImageManager/Validation', $this);
        if(!$validation) {
            $this->setJSONResponseOutput([
                'success' => false,
                'error' => 'Валидацията не е настроена'
            ]);
            return;
        }
        
        $validationResult = $validation->validateFolderName($folderName);

        if (!$validationResult['valid']) {
            $this->setJSONResponseOutput([
                'success' => false,
                'error' => $validationResult['error']
            ]);
            return;
        }

        // Създаване на папката
        $directory = $this->setBackendSubController('Common/ImageManager/Directory', $this);
        $createResult = $directory->createFolder($folderName, $currentDirectory);

        $this->setJSONResponseOutput($createResult);
    }

   
}
