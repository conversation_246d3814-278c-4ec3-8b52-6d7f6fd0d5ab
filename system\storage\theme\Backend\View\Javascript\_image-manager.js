/**
 * ImageManager модул за управление на изображения в администрацията на OpenCart
 * Независим модул за селекция на изображения с callback архитектура
 */
(function() {
    'use strict';

    // Глобален ImageManager модул
    window.ImageManager = {
        // Конфигурация
        config: {
            currentDirectory: '',
            selectedImages: [],
            isOpen: false,
            lastUploadDirectory: '',
            lastSelectedElement: null,
            allImageElements: [],
            searchQuery: '',
            allItems: [],
            filteredItems: [],
            searchTimeout: null,
            createFolderUrl: '',
            singleSelectionMode: false,
            onSelectCallback: null, // Callback функция за връщане на селектираните изображения
            pagination: {
                currentPage: 1,
                limit: 30,
                hasMore: false,
                loading: false
            }
        },

        /**
         * Отваряне на ImageManager с callback функция
         * @param {Object} options - Опции за конфигурация
         * @param {boolean} options.singleSelection - Режим на селекция (single/multi)
         * @param {function} options.onSelect - Callback функция за обработка на селектираните изображения
         * @param {string} options.startDirectory - Начална директория за отваряне
         */
        open: function(options = {}) {
            this.config.singleSelectionMode = options.singleSelection || false;
            this.config.onSelectCallback = options.onSelect || null;
            this.config.startDirectory = options.startDirectory || '';

            this.loadTemplate();
        },

        /**
         * Затваряне на ImageManager
         */
        close: function() {
            const modal = document.getElementById('image-manager-modal');
            if (modal) {
                modal.style.display = 'none';
                this.config.isOpen = false;
                this.config.singleSelectionMode = false;
                this.config.onSelectCallback = null;
                this.clearSelection();
            }
        },

        /**
         * Навигация нагоре в директорията
         */
        navigateUp: function() {
            const currentDir = this.config.currentDirectory;

            console.log('ImageManager: navigateUp() извикан с currentDir:', currentDir);

            // Ако сме в root директорията, не можем да отидем по-нагоре
            if (!currentDir || currentDir === '') {
                console.log('ImageManager: Вече сме в root директорията');
                return;
            }

            const parts = currentDir.split('/').filter(part => part !== '');
            console.log('ImageManager: Части на пътя:', parts);

            if (parts.length > 1) {
                // Премахваме последната част
                parts.pop();
                const parentDir = parts.join('/');
                console.log('ImageManager: Навигация към родителска директория:', parentDir);
                this.navigateToDirectory(parentDir);
            } else if (parts.length === 1) {
                // Отиваме в root директорията
                console.log('ImageManager: Навигация към root директория');
                this.navigateToDirectory('');
            }
        },

        /**
         * Затваряне на ImageManager (алиас за backward compatibility)
         */
        closeImageManager: function() {
            this.close();
        },

        /**
         * Зареждане на template за ImageManager
         */
        loadTemplate: function() {
            if (!document.getElementById('image-manager-modal')) {
                const userToken = (typeof BackendModule !== 'undefined' && BackendModule.config) 
                    ? BackendModule.config.userToken 
                    : '';
                    
                fetch('index.php?route=common/imagemanager/template&user_token=' + userToken, {
                    cache: 'no-store',
                    headers: { 
                        'Cache-Control': 'no-cache, no-store, must-revalidate', 
                        'Pragma': 'no-cache', 
                        'Expires': '0' 
                    }
                })
                .then(response => response.text())
                .then(html => {
                    document.body.insertAdjacentHTML('beforeend', html);
                    this.initModal();
                    this.showModal();
                })
                .catch(error => {
                    console.error('Грешка при зареждане на template:', error);
                });
            } else {
                this.showModal();
            }
        },

        /**
         * Показване на модала
         */
        showModal: function(startDirectory = '') {
            const modal = document.getElementById('image-manager-modal');
            if (modal) {
                // Задаваме правилни стилове за центриране и размер
                modal.style.display = 'flex';
                modal.style.position = 'fixed';
                modal.style.top = '0';
                modal.style.left = '0';
                modal.style.width = '100%';
                modal.style.height = '100%';
                modal.style.backgroundColor = 'rgba(0, 0, 0, 0.5)';
                modal.style.justifyContent = 'center';
                modal.style.alignItems = 'center';
                modal.style.zIndex = '9999';

                // Задаваме размер на модала - 80% от viewport
                const modalContent = modal.querySelector('.bg-white');
                if (modalContent) {
                    modalContent.style.width = '80vw';
                    modalContent.style.height = '70vh';
                    modalContent.style.maxWidth = '1200px';
                    modalContent.style.maxHeight = '800px';
                }

                this.config.isOpen = true;

                // Използваме предадената директория или конфигурираната
                const directoryToLoad = startDirectory || this.config.startDirectory || '';
                this.config.currentDirectory = directoryToLoad;
                this.config.selectedImages = [];

                console.log('ImageManager: Отваряне на директория:', directoryToLoad);
                this.loadDirectoryContents(directoryToLoad);
                this.updateSelectionModeUI();
            }
        },

        /**
         * Инициализация на модала
         */
        initModal: function() {
            const modal = document.getElementById('image-manager-modal');
            if (!modal) return;

            // Event listeners за модала
            modal.addEventListener('click', (e) => {
                // Проверяваме дали кликът е върху съдържанието на модала (бялата част)
                // Ако кликът е върху .bg-white елемента или негов дъщерен елемент, НЕ затваряме модала
                if (!e.target.closest('.bg-white')) {
                    // Кликът е извън съдържанието (върху overlay фона) - затваряме модала
                    this.close();
                    return;
                }

                // Използваме closest() за да намерим най-близкия родителски елемент с ID
                const targetElement = e.target.closest('[id]') || e.target;
                const targetId = targetElement.id;

                if (targetId === 'close-image-manager') {
                    this.close();
                } else if (targetId === 'navigate-up') {
                    this.navigateUp();
                } else if (targetId === 'refresh-manager') {
                    this.refreshManager();
                } else if (targetId === 'upload-files-btn') {
                    document.getElementById('upload-files-input').click();
                } else if (targetId === 'create-folder-btn') {
                    this.showCreateFolderDialog();
                } else if (targetId === 'clear-selection') {
                    this.clearSelection();
                } else if (targetId === 'add-selected') {
                    this.addSelectedImages();
                } else if (targetId === 'clear-search') {
                    this.clearSearch();
                }
            });

            // Search functionality with debouncing
            const searchInput = document.getElementById('search-images');
            if (searchInput) {
                searchInput.addEventListener('input', (e) => {
                    this.handleSearchWithDebounce(e.target.value);
                });
            }

            // Upload files input
            const uploadInput = document.getElementById('upload-files-input');
            if (uploadInput) {
                uploadInput.addEventListener('change', (e) => {
                    const files = Array.from(e.target.files);
                    if (files.length > 0) {
                        this.uploadFilesToManager(files);
                    }
                    e.target.value = ''; // Reset input
                });
            }

            // Infinite scroll functionality
            this.initInfiniteScroll();

            // Create folder dialog event listeners
            this.initCreateFolderDialog();
        },

        /**
         * Създаване на бутон "Избери от библиотеката" ако не съществува
         */
        createLibraryButton: function() {
            // Тази функция се извиква от оригиналния код
            // Може да се имплементира ако е необходимо
        },

        /**
         * Получава размерите на браузера за изпращане към сървъра
         */
        getModalDimensions: function() {
            const itemsGrid = document.getElementById('items-grid');
            const itemsGridParent = itemsGrid ? itemsGrid.parentElement : null;

            // Използваме размерите на браузера вместо модала
            const viewportWidth = window.innerWidth;
            const viewportHeight = window.innerHeight;

            // За grid ширината използваме 80% от viewport ширината като приблизителна стойност
            // или реалната ширина на grid-а ако е наличен
            const gridWidth = itemsGridParent ? (itemsGridParent.offsetWidth - 32) : Math.floor(viewportWidth * 0.8);

            return {
                width: viewportWidth,
                height: viewportHeight,
                gridWidth: gridWidth
            };
        },

        /**
         * Изчислява оптималния брой изображения за зареждане въз основа на размерите на модала
         */
        calculateOptimalImageCount: function() {
            const itemsGrid = document.getElementById('items-grid');
            const itemsGridParent = itemsGrid ? itemsGrid.parentElement : null;

            // Използваме размерите на браузера вместо модала
            const viewportWidth = window.innerWidth;
            const viewportHeight = window.innerHeight;

            // Изчисляваме приблизителната височина на модала (70% от viewport)
            const modalHeight = Math.floor(viewportHeight * 0.7);

            // Изчисляваме достъпната височина за grid (отчитаме modal header, breadcrumb, toolbar, footer)
            const availableHeight = modalHeight - 150; // по-консервативна оценка

            // Приблизителни размери на един grid item (включително gap)
            const itemHeight = 120; // височина на изображение + текст + padding
            const itemWidth = 100;  // ширина на изображение + padding
            const gridGap = 8;      // gap между елементите

            // За grid ширината използваме 80% от viewport ширината като приблизителна стойност
            const gridWidth = itemsGridParent ? (itemsGridParent.offsetWidth - 32) : Math.floor(viewportWidth * 0.8);

            // Изчисляваме колко колони се побират в ширината
            const columnsPerRow = Math.max(1, Math.floor(gridWidth / (itemWidth + gridGap)));

            // Изчисляваме колко реда се побират във височината
            const rowsVisible = Math.max(1, Math.floor(availableHeight / (itemHeight + gridGap)));

            // Общ брой видими изображения
            const visibleImages = columnsPerRow * rowsVisible;

            // Добавяме още 1-2 реда за да се създаде scrollbar и да се активира infinite scroll
            const optimalCount = visibleImages + (columnsPerRow * 2);

            // Минимум 12, максимум 60 изображения (увеличени лимити за по-голяма гъвкавост)
            return Math.max(12, Math.min(60, optimalCount));
        },

        /**
         * Получаване на последното качено изображение
         */
        getLastUploadedImage: function() {
            const images = document.querySelectorAll('#tab-images .group img');
            return images.length > 0 ? images[images.length - 1] : null;
        },

        /**
         * Определяване на началната директория въз основа на последното качено изображение
         */
        getStartDirectory: function() {
            // Определяваме началната директория
            let startDirectory = this.config.lastUploadDirectory || '';

            // Ако има качени изображения, отиваме в папката на последното
            const lastImage = this.getLastUploadedImage();
            if (lastImage) {
                const imagePath = lastImage.src || lastImage.getAttribute('src') || '';
                if (imagePath) {
                    const pathParts = imagePath.split('/');
                    // Премахваме протокола, домейна и cache частите
                    const catalogIndex = pathParts.findIndex(part => part === 'catalog');
                    if (catalogIndex !== -1) {
                        // Взимаме частта след 'catalog' и премахваме файла
                        const relevantParts = pathParts.slice(catalogIndex + 1);
                        relevantParts.pop(); // Премахваме файла
                        startDirectory = relevantParts.join('/');
                    }
                }
            }

            return startDirectory;
        },

        /**
         * Запазване на последната директория за качване
         */
        setLastUploadDirectory: function(directory) {
            this.config.lastUploadDirectory = directory;
        },

        /**
         * Навигация нагоре в директорията
         */
        navigateUp: function() {
            const currentPath = this.config.currentDirectory;
            if (currentPath) {
                const pathParts = currentPath.split('/');
                pathParts.pop();
                const newPath = pathParts.join('/');
                this.loadDirectory(newPath);
            }
        },

        /**
         * Обновяване на мениджъра
         */
        refreshManager: function() {
            this.loadDirectoryContents(this.config.currentDirectory);
        },

        /**
         * Показване на диалог за създаване на папка
         */
        showCreateFolderDialog: function() {
            const folderName = prompt('Въведете име на новата папка:');
            if (folderName && folderName.trim()) {
                this.createFolder(folderName.trim());
            }
        },

        /**
         * Създаване на нова папка
         */
        createFolder: function(folderName) {
            const userToken = (typeof BackendModule !== 'undefined' && BackendModule.config)
                ? BackendModule.config.userToken
                : '';

            const formData = new FormData();
            formData.append('directory', this.config.currentDirectory);
            formData.append('name', folderName);

            fetch('index.php?route=common/imagemanager/createfolder&user_token=' + userToken, {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    this.refreshManager();
                } else {
                    alert('Грешка при създаване на папка: ' + (data.error || 'Неизвестна грешка'));
                }
            })
            .catch(error => {
                console.error('Грешка при създаване на папка:', error);
                alert('Грешка при създаване на папка');
            });
        },

        /**
         * Инициализация на event listeners
         */
        initEventListeners: function() {
            const modal = document.getElementById('image-manager-modal');
            if (!modal) return;

            // Затваряне на модала
            const closeBtn = modal.querySelector('.close-modal');
            if (closeBtn) {
                closeBtn.addEventListener('click', () => this.close());
            }

            // Event listeners за модала (като в оригиналния код)
            modal.addEventListener('click', (e) => {
                // Проверяваме дали кликът е върху съдържанието на модала (бялата част)
                if (!e.target.closest('.bg-white')) {
                    // Кликът е извън съдържанието (върху overlay фона) - затваряме модала
                    this.closeImageManager();
                    return;
                }

                // Използваме closest() за да намерим най-близкия родителски елемент с ID
                const targetElement = e.target.closest('[id]') || e.target;
                const targetId = targetElement.id;

                // Обработка на различните бутони
                if (targetId === 'close-image-manager') {
                    this.closeImageManager();
                } else if (targetId === 'navigate-up') {
                    this.navigateUp();
                } else if (targetId === 'refresh-manager') {
                    this.refreshManager();
                } else if (targetId === 'upload-files-btn') {
                    const uploadInput = document.getElementById('upload-files-input');
                    if (uploadInput) uploadInput.click();
                } else if (targetId === 'create-folder-btn') {
                    this.showCreateFolderDialog();
                } else if (targetId === 'select-images-btn') {
                    this.selectImages();
                } else if (targetId === 'clear-selection-btn') {
                    this.clearSelection();
                }
            });

            // Премахваме global event listeners - те се обработват от ProductForm модула

            // Избор на изображения
            const imageGrid = modal.querySelector('#image-grid');
            if (imageGrid) {
                imageGrid.addEventListener('click', (e) => this.handleImageClick(e));
            }

            // Навигация в папки
            const breadcrumb = modal.querySelector('#breadcrumb');
            if (breadcrumb) {
                breadcrumb.addEventListener('click', (e) => this.handleBreadcrumbClick(e));
            }

            // Бутон за избор
            const selectBtn = modal.querySelector('#select-images-btn');
            if (selectBtn) {
                selectBtn.addEventListener('click', () => this.selectImages());
            }

            // Бутон за отказ
            const cancelBtn = modal.querySelector('#cancel-btn');
            if (cancelBtn) {
                cancelBtn.addEventListener('click', () => this.close());
            }
        },

        /**
         * Обработка на клик върху изображение
         */
        handleImageClick: function(e) {
            const imageElement = e.target.closest('.image-item');
            if (!imageElement) return;

            const isFolder = imageElement.classList.contains('folder-item');
            
            if (isFolder) {
                const folderPath = imageElement.dataset.path;
                this.loadDirectory(folderPath);
            } else {
                this.handleImageSelection(imageElement, e);
            }
        },

        /**
         * Обработка на селекция на изображение
         */
        handleImageSelection: function(element, event) {
            const path = element.dataset.path;
            const name = element.dataset.name;
            const thumb = element.dataset.thumb;

            if (this.config.singleSelectionMode) {
                // Single selection режим
                this.clearSelection();
                this.selectSingleImage(element, path, name, thumb);
            } else {
                // Multi-selection режим
                if (event.ctrlKey || event.metaKey) {
                    this.toggleImageSelection(element, path, name, thumb);
                } else if (event.shiftKey && this.config.lastSelectedElement) {
                    this.selectImageRange(this.config.lastSelectedElement, element);
                } else {
                    this.clearSelection();
                    this.selectSingleImage(element, path, name, thumb);
                }
            }

            this.config.lastSelectedElement = element;
            this.updateSelectButton();
        },

        /**
         * Селектиране на единично изображение
         */
        selectSingleImage: function(element, path, name, thumb) {
            element.classList.add('selected');
            this.config.selectedImages = [{
                path: path,
                name: name,
                thumb: thumb
            }];
        },

        /**
         * Toggle селекция на изображение
         */
        toggleImageSelection: function(element, path, name, thumb) {
            const isSelected = element.classList.contains('selected');
            
            if (isSelected) {
                element.classList.remove('selected');
                this.config.selectedImages = this.config.selectedImages.filter(img => img.path !== path);
            } else {
                element.classList.add('selected');
                this.config.selectedImages.push({
                    path: path,
                    name: name,
                    thumb: thumb
                });
            }
        },

        /**
         * Изчистване на селекцията
         */
        clearSelection: function() {
            const selectedElements = document.querySelectorAll('#image-grid .image-item.selected');
            selectedElements.forEach(el => el.classList.remove('selected'));
            this.config.selectedImages = [];
        },

        /**
         * Актуализиране на UI според режима на селекция
         */
        updateSelectionModeUI: function() {
            const selectBtn = document.querySelector('#select-images-btn');
            if (selectBtn) {
                if (this.config.singleSelectionMode) {
                    selectBtn.textContent = 'Избери';
                } else {
                    selectBtn.textContent = 'Избери изображения';
                }
            }
        },

        /**
         * Актуализиране на бутона за избор
         */
        updateSelectButton: function() {
            const selectBtn = document.querySelector('#select-images-btn');
            if (selectBtn) {
                const count = this.config.selectedImages.length;
                if (count > 0) {
                    selectBtn.disabled = false;
                    if (this.config.singleSelectionMode) {
                        selectBtn.textContent = 'Избери';
                    } else {
                        selectBtn.textContent = `Избери ${count} изображения`;
                    }
                } else {
                    selectBtn.disabled = true;
                    selectBtn.textContent = this.config.singleSelectionMode ? 'Избери' : 'Избери изображения';
                }
            }
        },

        /**
         * Финализиране на избора и извикване на callback
         */
        selectImages: function() {
            if (this.config.selectedImages.length === 0) return;

            if (typeof this.config.onSelectCallback === 'function') {
                if (this.config.singleSelectionMode) {
                    // За single selection връщаме само първото изображение
                    this.config.onSelectCallback(this.config.selectedImages[0]);
                } else {
                    // За multi-selection връщаме масив
                    this.config.onSelectCallback(this.config.selectedImages);
                }
            }

            this.close();
        },

        /**
         * Зареждане на съдържанието на директория
         */
        loadDirectoryContents: function(directory = '', append = false) {
            console.log('ImageManager: loadDirectoryContents() извикан с:', {
                directory: directory,
                append: append,
                currentDirectory: this.config.currentDirectory
            });

            directory = directory || '';
            this.config.currentDirectory = directory;

            if (!append) {
                this.config.pagination.currentPage = 1;
                this.config.pagination.hasMore = false;
            }

            this.config.pagination.loading = true;

            // Изчисляваме динамично броя изображения за зареждане
            const dynamicLimit = this.calculateOptimalImageCount();

            // Получаваме размерите на модала за изпращане към сървъра
            const modalDimensions = this.getModalDimensions();

            const userToken = (typeof BackendModule !== 'undefined' && BackendModule.config)
                ? BackendModule.config.userToken
                : '';

            const page = append ? this.config.pagination.currentPage + 1 : 1;

            // Добавяме размерите на модала към URL параметрите
            const url = `index.php?route=common/imagemanager&user_token=${userToken}&directory=${encodeURIComponent(directory)}&page=${page}&limit=${dynamicLimit}&modal_width=${modalDimensions.width}&modal_height=${modalDimensions.height}&grid_width=${modalDimensions.gridWidth}`;

            // Запазваме следващата страница за актуализация
            const nextPage = page;

            console.log('ImageManager: AJAX URL:', url);

            fetch(url, {
                cache: 'no-store',
                headers: {
                    'Cache-Control': 'no-cache, no-store, must-revalidate',
                    'Pragma': 'no-cache',
                    'Expires': '0'
                }
            })
            .then(response => response.json())
            .then(data => {
                console.log('ImageManager: Получени данни:', data);

                if (append) {
                    this.appendDirectoryContents(data);
                } else {
                    this.renderDirectoryContents(data);
                }

                this.updateBreadcrumb(data.breadcrumb);

                // Актуализираме pagination състоянието
                this.config.pagination.loading = false;
                this.config.pagination.currentPage = nextPage;
                this.config.pagination.hasMore = data.pagination && data.pagination.hasMore;
            })
            .catch(error => {
                console.error('Грешка при зареждане на директория:', error);
                this.config.pagination.loading = false;
            });
        },

        /**
         * Рендериране на съдържанието на директория
         */
        renderDirectoryContents: function(data) {
            const itemsGrid = document.getElementById('items-grid');

            if (!itemsGrid) {
                console.error('ImageManager: Не е намерен items-grid елемент');
                return;
            }

            const gridContainer = itemsGrid.querySelector('.grid');
            if (!gridContainer) {
                console.error('ImageManager: Не е намерен grid контейнер в items-grid');
                return;
            }

            // Запазваме всички елементи за търсене
            this.config.allItems = data.items || [];
            this.config.filteredItems = [...this.config.allItems];

            console.log('ImageManager: Рендиране на елементи:', this.config.filteredItems.length);

            this.renderItems(this.config.filteredItems);
        },

        /**
         * Добавя нови елементи към съществуващите (за infinite scroll)
         */
        appendDirectoryContents: function(data) {
            if (!data.items || data.items.length === 0) return;

            const gridContainer = document.getElementById('items-grid').querySelector('.grid');
            if (!gridContainer) return;

            // Филтрираме само изображенията (папките вече са заредени)
            const newImages = data.items.filter(item => item.type === 'image');

            console.log('ImageManager: Добавяне на нови изображения:', newImages.length);

            // Добавяме новите елементи към allItems
            this.config.allItems = [...this.config.allItems, ...newImages];
            this.config.filteredItems = [...this.config.allItems];

            // Рендираме новите елементи
            newImages.forEach(item => {
                const itemElement = this.createItemElement(item);
                gridContainer.appendChild(itemElement);

                // Зареждане на thumbnail за изображения
                if (item.type === 'image') {
                    setTimeout(() => {
                        this.loadImageThumbnail(item, itemElement);
                    }, 10);
                }
            });

            // Актуализираме списъка с image елементи
            setTimeout(() => {
                this.updateImageElementsList();
            }, 100);
        },

        /**
         * Рендира елементите в grid-а
         */
        renderItems: function(items) {
            const itemsGrid = document.getElementById('items-grid');
            const emptyState = document.getElementById('empty-state');
            const gridContainer = itemsGrid.querySelector('.grid');

            if (!gridContainer) return;

            gridContainer.innerHTML = '';

            if (items && items.length > 0) {
                items.forEach(item => {
                    const itemElement = this.createItemElement(item);
                    gridContainer.appendChild(itemElement);

                    // Зареждане на thumbnail за изображения (cached или lazy loading)
                    if (item.type === 'image') {
                        // Малко забавяне за да се осигури че DOM елементът е готов
                        const self = this;
                        setTimeout(function() {
                            self.loadImageThumbnail(item, itemElement);
                        }, 10);
                    }
                });

                itemsGrid.classList.remove('hidden');
                if (emptyState) emptyState.classList.add('hidden');

                // Актуализираме списъка с image елементи след рендиране
                setTimeout(() => {
                    this.updateImageElementsList();
                }, 100);
            } else {
                itemsGrid.classList.add('hidden');
                if (emptyState) emptyState.classList.remove('hidden');
            }
        },

        /**
         * Създаване на елемент за файл/папка
         */
        createItemElement: function(item) {
            const element = document.createElement('div');

            if (item.type === 'directory') {
                element.className = 'directory-item';
                element.innerHTML = `
                    <div class="aspect-square rounded overflow-hidden bg-gray-100 flex items-center justify-center">
                        <i class="ri-folder-line text-5xl text-gray-400"></i>
                    </div>
                    <p class="text-xs text-gray-600 mt-1 truncate" title="${item.name}">${item.name}</p>
                `;

                element.addEventListener('click', () => {
                    const newPath = `/${this.ltrim(item.path, '/')}`
                    console.log('ImageManager: Навигация към папка:', newPath);
                    this.navigateToDirectory(newPath);
                });
            } else {
                element.className = 'image-item';

                // За изображения без cache използваме placeholder, за cached - директно thumb URL
                const imgSrc = item.has_cache ? item.thumb : 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgdmlld0JveD0iMCAwIDEwMCAxMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIxMDAiIGhlaWdodD0iMTAwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0zNSA0MEg2NVY2MEgzNVY0MFoiIGZpbGw9IiNEMUQ1REIiLz4KPC9zdmc+';
                const imgClass = item.has_cache ? 'w-full h-full object-cover' : 'w-full h-full object-cover loading';

                // Проверяваме за предупреждения за формата
                const formatWarning = this.getFormatWarningHtml(item);

                element.innerHTML = `
                    <div class="aspect-square rounded overflow-hidden relative">
                        <img src="${imgSrc}" alt="${item.name}" class="${imgClass}">
                        ${formatWarning}
                    </div>
                    <p class="text-xs text-gray-600 mt-1 truncate" title="${item.name}">${item.name}</p>
                `;

                // Добавяме data атрибути за селекция
                element.dataset.path = item.path;
                element.dataset.name = item.name;
                element.dataset.thumb = item.thumb;

                // Click event за селекция
                element.addEventListener('click', (e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    this.handleImageSelection(element, e);
                });
            }

            return element;
        },

        /**
         * Зарежда thumbnail за изображение (lazy loading)
         */
        loadImageThumbnail: function(item, itemElement) {
            const img = itemElement.querySelector('img');
            if (!img) {
                console.warn('Няма img елемент в itemElement за:', item.name);
                return;
            }

            // Ако изображението вече има cache, не правим нищо
            if (item.has_cache) {
                img.classList.remove('loading');
                return;
            }

            // Зареждаме thumbnail чрез AJAX
            const userToken = (typeof BackendModule !== 'undefined' && BackendModule.config)
                ? BackendModule.config.userToken
                : '';

            const thumbUrl = `index.php?route=common/imagemanager/thumb&user_token=${userToken}&image=${encodeURIComponent(item.path)}`;

            fetch(thumbUrl)
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.thumb) {
                        img.src = data.thumb;
                        img.classList.remove('loading');
                    }
                })
                .catch(error => {
                    console.warn('Грешка при зареждане на thumbnail за:', item.name, error);
                    img.classList.remove('loading');
                });
        },

        /**
         * Получава HTML за предупреждение за формата на файла
         */
        getFormatWarningHtml: function(item) {
            // Тази функция може да се имплементира за показване на предупреждения
            return '';
        },

        /**
         * Премахва символи от началото на стринг
         */
        ltrim: function(str, chars) {
            chars = chars || '\\s';
            return str.replace(new RegExp('^[' + chars + ']+'), '');
        },

        /**
         * Премахва символи от края на стринг
         */
        rtrim: function(str, charToRemove) {
            while (str.endsWith(charToRemove)) {
                str = str.slice(0, -1);
            }
            return str;
        },

        /**
         * Навигация към директория
         */
        navigateToDirectory: function(directory) {
            this.loadDirectoryContents(directory);
        },

        /**
         * Актуализира списъка с image елементи
         */
        updateImageElementsList: function() {
            const imageElements = document.querySelectorAll('#items-grid .image-item');
            this.config.allImageElements = Array.from(imageElements);
        },

        /**
         * Актуализиране на breadcrumb навигацията
         */
        updateBreadcrumb: function(breadcrumbData) {
            const breadcrumbNav = document.getElementById('breadcrumb-nav');
            if (!breadcrumbNav) return;

            breadcrumbNav.innerHTML = '';

            // Ако няма breadcrumb данни, генерираме ги от текущата директория
            if (!breadcrumbData || !Array.isArray(breadcrumbData)) {
                breadcrumbData = this.generateBreadcrumbData(this.config.currentDirectory);
            }

            breadcrumbData.forEach((item, index) => {
                const isLast = index === breadcrumbData.length - 1;

                const breadcrumbItem = document.createElement('span');
                breadcrumbItem.className = `breadcrumb-item ${isLast ? 'active' : ''}`;
                breadcrumbItem.textContent = item.name;
                breadcrumbItem.dataset.path = item.path;

                // Всички breadcrumb елементи са кликаеми, включително последният
                breadcrumbItem.addEventListener('click', () => {
                    this.navigateToDirectory(item.path);
                });

                breadcrumbNav.appendChild(breadcrumbItem);

                // Добавяме разделител ако не е последният
                if (!isLast) {
                    const separator = document.createElement('span');
                    separator.className = 'text-gray-400 mx-2';
                    separator.innerHTML = '<i class="ri-arrow-right-s-line"></i>';
                    breadcrumbNav.appendChild(separator);
                }
            });
        },

        /**
         * Генериране на breadcrumb данни от директория
         */
        generateBreadcrumbData: function(directory) {
            const breadcrumbData = [{ name: 'Начало', path: '' }];

            if (directory) {
                const parts = directory.split('/').filter(part => part);
                let currentPath = '';

                parts.forEach(part => {
                    currentPath += (currentPath ? '/' : '') + part;
                    breadcrumbData.push({ name: part, path: currentPath });
                });
            }

            return breadcrumbData;
        },

        /**
         * Обработва търсенето с debouncing
         */
        handleSearchWithDebounce: function(query) {
            // Изчистваме предишния timeout
            if (this.config.searchTimeout) {
                clearTimeout(this.config.searchTimeout);
            }

            // Задаваме нов timeout за 500ms
            this.config.searchTimeout = setTimeout(() => {
                this.handleSearch(query);
            }, 500);
        },

        /**
         * Обработва търсенето
         */
        handleSearch: function(query) {
            this.config.searchQuery = query.toLowerCase().trim();

            const clearButton = document.getElementById('clear-search');
            if (clearButton) {
                if (this.config.searchQuery) {
                    clearButton.classList.remove('hidden');
                } else {
                    clearButton.classList.add('hidden');
                }
            }

            if (!this.config.searchQuery) {
                // Показваме всички елементи
                this.config.filteredItems = [...this.config.allItems];
            } else {
                // Филтрираме елементите
                this.config.filteredItems = this.config.allItems.filter(item => {
                    return item.name.toLowerCase().includes(this.config.searchQuery);
                });

                console.log('ImageManager: Търсене за "' + this.config.searchQuery + '" намери ' + this.config.filteredItems.length + ' резултата');
            }

            this.renderItems(this.config.filteredItems);
        },

        /**
         * Изчиства търсенето
         */
        clearSearch: function() {
            // Изчистваме timeout ако има такъв
            if (this.config.searchTimeout) {
                clearTimeout(this.config.searchTimeout);
                this.config.searchTimeout = null;
            }

            const searchInput = document.getElementById('search-images');
            const clearButton = document.getElementById('clear-search');

            if (searchInput) searchInput.value = '';
            if (clearButton) clearButton.classList.add('hidden');

            this.config.searchQuery = '';
            this.config.filteredItems = [...this.config.allItems];
            this.renderItems(this.config.filteredItems);
        },

        /**
         * Извършва рекурсивно търсене в подпапки
         */
        performRecursiveSearch: function() {
            // За рекурсивното търсене ще направим AJAX заявка към сървъра
            const userToken = this.getUserToken();
            const url = `index.php?route=common/imagemanager/search&user_token=${userToken}&query=${encodeURIComponent(this.config.searchQuery)}&directory=${encodeURIComponent(this.config.currentDirectory)}`;

            fetch(url, {
                    cache: 'no-store',
                    headers: { 'Cache-Control': 'no-cache, no-store, must-revalidate', 'Pragma': 'no-cache', 'Expires': '0' }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.items) {
                        // Комбинираме резултатите от рекурсивното търсене
                        const recursiveResults = data.items.filter(item => {
                            // Избягваме дублиране на елементи от текущата директория
                            return !this.config.filteredItems.find(existing => existing.path === item.path);
                        });

                        this.config.filteredItems = [...this.config.filteredItems, ...recursiveResults];
                        this.renderItems(this.config.filteredItems);
                    }
                })
                .catch(error => {
                    console.error('Грешка при рекурсивно търсене:', error);
                });
        },

        /**
         * Селектиране на изображение
         */
        selectImage: function(element, path, name, thumb) {
            element.classList.add('selected');

            // Проверяваме дали вече е добавено
            const existingIndex = this.config.selectedImages.findIndex(img => img.path === path);
            if (existingIndex === -1) {
                this.config.selectedImages.push({
                    path: path,
                    name: name,
                    thumb: thumb
                });
            }
        },

        /**
         * Деселектиране на изображение
         */
        deselectImage: function(element, path) {
            element.classList.remove('selected');

            const index = this.config.selectedImages.findIndex(img => img.path === path);
            if (index !== -1) {
                this.config.selectedImages.splice(index, 1);
            }
        },

        /**
         * Добавяне на избраните изображения
         */
        addSelectedImages: function() {
            if (this.config.selectedImages.length === 0) return;

            if (this.config.onSelectCallback) {
                this.config.onSelectCallback(this.config.selectedImages);
                this.close();
            }
        },

        /**
         * Актуализиране на информацията за селекцията
         */
        updateSelectionInfo: function() {
            const selectionInfo = document.getElementById('selection-info');
            const addSelectedBtn = document.getElementById('add-selected');
            const clearSelectionBtn = document.getElementById('clear-selection');

            if (selectionInfo) {
                const count = this.config.selectedImages.length;
                if (count > 0) {
                    selectionInfo.textContent = `Избрани: ${count} изображения`;
                    selectionInfo.classList.remove('hidden');
                } else {
                    selectionInfo.classList.add('hidden');
                }
            }

            if (addSelectedBtn) {
                if (this.config.selectedImages.length > 0) {
                    addSelectedBtn.classList.remove('hidden');
                } else {
                    addSelectedBtn.classList.add('hidden');
                }
            }

            if (clearSelectionBtn) {
                if (this.config.selectedImages.length > 0) {
                    clearSelectionBtn.classList.remove('hidden');
                } else {
                    clearSelectionBtn.classList.add('hidden');
                }
            }
        },

        /**
         * Обработва range selection (Shift+Click)
         */
        handleRangeSelection: function(currentElement) {
            const lastIndex = this.config.allImageElements.indexOf(this.config.lastSelectedElement);
            const currentIndex = this.config.allImageElements.indexOf(currentElement);

            if (lastIndex === -1 || currentIndex === -1) return;

            const startIndex = Math.min(lastIndex, currentIndex);
            const endIndex = Math.max(lastIndex, currentIndex);

            // Селектираме всички елементи в диапазона
            for (let i = startIndex; i <= endIndex; i++) {
                const element = this.config.allImageElements[i];
                const path = element.dataset.path;
                const name = element.dataset.name;
                const thumb = element.dataset.thumb;

                if (!this.config.selectedImages.find(img => img.path === path)) {
                    this.config.selectedImages.push({ path, name, thumb });
                    element.classList.add('selected');
                }
            }
        },





        /**
         * Инициализация на диалога за създаване на папка
         */
        initCreateFolderDialog: function() {
            const modal = document.getElementById('create-folder-modal');
            const folderNameInput = document.getElementById('folder-name-input');
            const cancelBtn = document.getElementById('cancel-create-folder');
            const confirmBtn = document.getElementById('confirm-create-folder');
            const errorDiv = document.getElementById('folder-name-error');

            if (!modal || !folderNameInput || !cancelBtn || !confirmBtn) {
                return;
            }

            // Cancel button
            cancelBtn.addEventListener('click', () => {
                this.hideCreateFolderDialog();
            });

            // Confirm button
            confirmBtn.addEventListener('click', () => {
                this.createFolder();
            });

            // Enter key in input
            folderNameInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    this.createFolder();
                } else if (e.key === 'Escape') {
                    this.hideCreateFolderDialog();
                }
            });

            // Clear error on input
            folderNameInput.addEventListener('input', () => {
                errorDiv.classList.add('hidden');
                errorDiv.textContent = '';
            });

            // Close on overlay click
            modal.addEventListener('click', (e) => {
                if (e.target === modal) {
                    this.hideCreateFolderDialog();
                }
            });
        },

        /**
         * Скрива диалога за създаване на папка
         */
        hideCreateFolderDialog: function() {
            const modal = document.getElementById('create-folder-modal');
            if (modal) {
                modal.classList.add('hidden');
            }
        },



        /**
         * Валидира името на папката
         */
        validateFolderName: function(name) {
            const errors = [];

            // Проверка за празно име
            if (!name || name.trim() === '') {
                errors.push('Името на папката не може да бъде празно');
                return errors;
            }

            // Проверка за дължина
            if (name.length > 255) {
                errors.push('Името на папката е твърде дълго (максимум 255 символа)');
            }

            // Проверка за забранени символи
            const invalidChars = /[<>:"/\\|?*\x00-\x1f]/;
            if (invalidChars.test(name)) {
                errors.push('Името на папката съдържа невалидни символи');
            }

            // Проверка за резервирани имена (Windows)
            const reservedNames = ['CON', 'PRN', 'AUX', 'NUL', 'COM1', 'COM2', 'COM3', 'COM4', 'COM5', 'COM6', 'COM7', 'COM8', 'COM9', 'LPT1', 'LPT2', 'LPT3', 'LPT4', 'LPT5', 'LPT6', 'LPT7', 'LPT8', 'LPT9'];
            if (reservedNames.includes(name.toUpperCase())) {
                errors.push('Името на папката е резервирано от системата');
            }

            // Проверка за точки в началото или края
            if (name.startsWith('.') || name.endsWith('.')) {
                errors.push('Името на папката не може да започва или завършва с точка');
            }

            return errors;
        },



        /**
         * Инициализира infinite scroll функционалността
         */
        initInfiniteScroll: function() {
            const modal = document.getElementById('image-manager-modal');
            if (!modal) return;

            // Търсим правилния scroll контейнер
            const contentArea = modal.querySelector('.max-h-96.overflow-y-auto') ||
                              modal.querySelector('[style*="max-height"]') ||
                              modal.querySelector('.overflow-y-auto');

            if (!contentArea) {
                console.warn('ImageManager: Не е намерен scroll контейнер за infinite scroll');
                return;
            }

            contentArea.addEventListener('scroll', () => {
                // Проверяваме дали сме близо до края на контейнера
                const scrollTop = contentArea.scrollTop;
                const scrollHeight = contentArea.scrollHeight;
                const clientHeight = contentArea.clientHeight;

                // Ако сме на 90% от скрола и има още данни за зареждане
                if (scrollTop + clientHeight >= scrollHeight * 0.9) {
                    this.loadMoreImages();
                }
            });
        },

        /**
         * Зарежда още изображения (infinite scroll)
         */
        loadMoreImages: function() {
            if (this.config.pagination.loading || !this.config.pagination.hasMore) {
                return;
            }

            this.config.pagination.loading = true;
            const nextPage = this.config.pagination.currentPage + 1;

            console.log('ImageManager: Зареждане на страница', nextPage);

            this.loadDirectoryContents(this.config.currentDirectory, nextPage);
        },



        /**
         * Показване на progress за качване
         */
        showUploadProgress: function() {
            const modal = document.getElementById('upload-progress-modal');
            if (modal) {
                modal.classList.remove('hidden');
            }
        },

        /**
         * Скриване на progress за качване
         */
        hideUploadProgress: function() {
            const modal = document.getElementById('upload-progress-modal');
            if (modal) {
                modal.classList.add('hidden');
            }
        },

        /**
         * Обработка на клик върху breadcrumb
         */
        handleBreadcrumbClick: function(e) {
            const breadcrumbItem = e.target.closest('.breadcrumb-item');
            if (breadcrumbItem) {
                const path = breadcrumbItem.dataset.path;
                this.loadDirectoryContents(path);
            }
        },

        /**
         * Инициализация на търсене
         */
        initSearch: function() {
            const searchInput = document.querySelector('#image-search');
            if (searchInput) {
                searchInput.addEventListener('input', (e) => this.handleSearch(e.target.value));
            }
        },

        /**
         * Качване на единичен файл
         */
        uploadSingleFile: function(file) {
            const formData = new FormData();
            formData.append('files[]', file);
            formData.append('directory', this.config.currentDirectory || '');

            const userToken = this.getUserToken();

            return fetch(`index.php?route=common/imagemanager/upload&user_token=${userToken}`, {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    console.log('Файлът беше качен успешно:', file.name);
                    return { success: true, file: file, data: data };
                } else {
                    console.error('Грешка при качване на файл:', data.error);
                    return { success: false, file: file, error: data.error };
                }
            })
            .catch(error => {
                console.error('Мрежова грешка при качване на файл:', error);
                return { success: false, file: file, error: error.message };
            });
        },

        /**
         * Добавяне на качените изображения към продукта
         */
        addUploadedImagesToProduct: function(uploadedFiles) {
            const imagesContainer = document.querySelector('#tab-images .grid');
            if (!imagesContainer) return;

            uploadedFiles.forEach(file => {
                if (file.success) {
                    // Тук ще добавим логиката за добавяне на изображението към продукта
                    console.log('Добавяне на изображение към продукта:', file.data);
                }
            });
        },

        /**
         * Качване на файлове в мениджъра
         */
        uploadFilesToManager: function(files) {
            if (!files || files.length === 0) return;

            // Показваме progress modal
            this.showUploadProgress();

            const userToken = this.getUserToken();
            const directory = this.config.currentDirectory || '';

            // Качваме файловете един по един
            let uploadedCount = 0;
            let totalFiles = files.length;

            Array.from(files).forEach((file) => {
                const formData = new FormData();
                formData.append('files[]', file);
                formData.append('directory', directory);

                fetch(`index.php?route=common/imagemanager/upload&user_token=${userToken}`, {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(() => {
                    uploadedCount++;

                    if (uploadedCount === totalFiles) {
                        // Всички файлове са качени
                        this.hideUploadProgress();
                        this.refreshManager();
                    }
                })
                .catch(error => {
                    console.error('Грешка при качване на файл:', error);
                    uploadedCount++;

                    if (uploadedCount === totalFiles) {
                        this.hideUploadProgress();
                        this.refreshManager();
                    }
                });
            });
        },

        /**
         * Получава user_token от сесията
         */
        getUserToken: function() {
            // 1. Проверяваме в глобалния обект
            if (window.user_token) {
                return window.user_token;
            }

            // 2. Проверяваме в meta тагове
            const tokenMeta = document.querySelector('meta[name="user_token"]');
            if (tokenMeta) {
                return tokenMeta.getAttribute('content');
            }

            // 3. Проверяваме в URL параметрите
            const urlParams = new URLSearchParams(window.location.search);
            if (urlParams.has('user_token')) {
                return urlParams.get('user_token');
            }

            // 4. Проверяваме в BackendModule конфигурацията
            if (window.BackendModule && window.BackendModule.config && window.BackendModule.config.userToken) {
                return window.BackendModule.config.userToken;
            }

            console.warn('ImageManager: Не може да се намери user_token');
            return '';
        },

        /**
         * Показва warning икона за изображения, които не могат да се заредят
         */
        showWarningIcon: function(imgElement) {
            // Заменяме изображението с червена warning икона (триъгълник с удивителен знак)
            imgElement.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjAiIGhlaWdodD0iNjAiIHZpZXdCb3g9IjAgMCA2MCA2MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTMwIDVMNTUgNTBINUwzMCA1WiIgZmlsbD0iI0ZCQkYyNCIgc3Ryb2tlPSIjRjU5RTBCIiBzdHJva2Utd2lkdGg9IjIiLz4KPHBhdGggZD0iTTMwIDIwVjM1IiBzdHJva2U9IiNGNTlFMEIiIHN0cm9rZS13aWR0aD0iMyIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIi8+CjxjaXJjbGUgY3g9IjMwIiBjeT0iNDIiIHI9IjIiIGZpbGw9IiNGNTlFMEIiLz4KPC9zdmc+';
            imgElement.classList.add('warning-icon');
            imgElement.style.filter = 'none';
            imgElement.style.objectFit = 'contain';
            imgElement.style.padding = '10px';
        },

        /**
         * Инициализация на пагинация
         */
        initPagination: function() {
            // Пагинацията ще бъде имплементирана при необходимост
        },

        /**
         * Селектиране на диапазон от изображения (Shift+Click)
         */
        selectImageRange: function(startElement, endElement) {
            const allImages = Array.from(document.querySelectorAll('#image-grid .image-item:not(.folder-item)'));
            const startIndex = allImages.indexOf(startElement);
            const endIndex = allImages.indexOf(endElement);

            const minIndex = Math.min(startIndex, endIndex);
            const maxIndex = Math.max(startIndex, endIndex);

            for (let i = minIndex; i <= maxIndex; i++) {
                const element = allImages[i];
                const path = element.dataset.path;
                const name = element.dataset.name;
                const thumb = element.dataset.thumb;

                if (!element.classList.contains('selected')) {
                    element.classList.add('selected');
                    this.config.selectedImages.push({
                        path: path,
                        name: name,
                        thumb: thumb
                    });
                }
            }
        }
    };

})();
