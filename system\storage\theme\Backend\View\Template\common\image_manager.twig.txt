<!-- Image Manager Modal -->
<div id="image-manager-modal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-lg shadow-xl w-full max-w-6xl max-h-[90vh] flex flex-col">
            <!-- Header -->
            <div class="flex items-center justify-between p-6 border-b border-gray-200">
                <h2 class="text-xl font-semibold text-gray-900">Мениджър на изображения</h2>
                <button type="button" id="close-image-manager" class="text-gray-400 hover:text-gray-600 transition-colors">
                    <i class="ri-close-line text-2xl"></i>
                </button>
            </div>
            
            <!-- Toolbar -->
            <div class="flex items-center justify-between p-4 border-b border-gray-200 bg-gray-50">
                <!-- Navigation -->
                <div class="flex items-center space-x-2">
                    <button type="button" id="navigate-up" class="p-2 bg-white border border-gray-300 rounded hover:bg-gray-50 transition-colors" title="Нагоре">
                        <i class="ri-arrow-up-line"></i>
                    </button>
                    <button type="button" id="refresh-manager" class="p-2 bg-white border border-gray-300 rounded hover:bg-gray-50 transition-colors" title="Обнови">
                        <i class="ri-refresh-line"></i>
                    </button>
                </div>

                <!-- Search -->
                <div class="flex-1 max-w-md mx-4">
                    <div class="relative">
                        <input type="text" id="search-images" placeholder="Търсене на изображения и папки..."
                               class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-primary">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <i class="ri-search-line text-gray-400"></i>
                        </div>
                        <button type="button" id="clear-search" class="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600 hidden">
                            <i class="ri-close-line"></i>
                        </button>
                    </div>
                </div>

                <!-- Upload -->
                <div class="flex items-center space-x-2">
                    <button type="button" id="upload-files-btn" class="px-4 py-2 bg-primary text-white rounded hover:bg-primary/90 transition-colors">
                        <i class="ri-upload-line mr-2"></i>
                        Качи файлове
                    </button>
                    <button type="button" id="create-folder-btn" class="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 transition-colors">
                        <i class="ri-folder-add-line mr-2"></i>
                        Създай папка
                    </button>
                    <input type="file" id="upload-files-input" multiple accept="image/*" class="hidden">
                </div>
            </div>
            
            <!-- Breadcrumb -->
            <div class="px-4 py-2 bg-gray-100 border-b border-gray-200">
                <nav id="breadcrumb-nav" class="flex items-center space-x-2 text-sm">
                    <!-- Breadcrumb items will be populated here -->
                </nav>
            </div>
            
            <!-- Content Area -->
            <div class="flex-1 overflow-hidden overflow-y-auto">
                <!-- Drop Zone Overlay -->
                <div id="drop-zone-overlay" class="absolute inset-0 bg-primary bg-opacity-20 border-4 border-dashed border-primary hidden z-10 flex items-center justify-center">
                    <div class="text-center">
                        <i class="ri-upload-cloud-line text-6xl text-primary mb-4"></i>
                        <p class="text-xl font-semibold text-primary">Пуснете файловете тук</p>
                    </div>
                </div>
                
                <!-- Loading Indicator -->
                <div id="loading-indicator" class="flex items-center justify-center h-64 hidden">
                    <div class="text-center">
                        <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
                        <p class="text-gray-600">Зареждане...</p>
                    </div>
                </div>
                
                <!-- Items Grid -->
                <div id="items-grid" class="p-4 h-full overflow-y-auto">
                    <div class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 xl:grid-cols-8 gap-4">
                        <!-- Items will be populated here -->
                    </div>
                </div>
                
                <!-- Empty State -->
                <div id="empty-state" class="flex items-center justify-center h-64 hidden">
                    <div class="text-center">
                        <i class="ri-folder-open-line text-6xl text-gray-300 mb-4"></i>
                        <p class="text-gray-500">Няма файлове в тази папка</p>
                    </div>
                </div>
            </div>
            
            <!-- Footer -->
            <div class="flex items-center justify-between p-4 border-t border-gray-200 bg-gray-50">
                <!-- Selection Info -->
                <div id="selection-info" class="text-sm text-gray-600">
                    <span id="selected-count">0</span> избрани файла
                </div>
                
                <!-- Actions -->
                <div class="flex items-center space-x-2">
                    <button type="button" id="clear-selection" class="px-4 py-2 bg-gray-200 text-gray-700 rounded hover:bg-gray-300 transition-colors">
                        Изчисти селекцията
                    </button>
                    <button type="button" id="add-selected" class="px-4 py-2 bg-primary text-white rounded hover:bg-primary/90 transition-colors" disabled>
                        Добави избраните
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Upload Progress Modal -->
<div id="upload-progress-modal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-60">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-lg shadow-xl w-full max-w-md">
            <div class="p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Качване на файлове</h3>
                
                <div id="upload-progress-list" class="space-y-3 mb-4">
                    <!-- Upload progress items will be populated here -->
                </div>
                
                <div class="flex justify-end">
                    <button type="button" id="close-upload-progress" class="px-4 py-2 bg-gray-200 text-gray-700 rounded hover:bg-gray-300 transition-colors" disabled>
                        Затвори
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Create Folder Modal -->
<div id="create-folder-modal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-60">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-lg shadow-xl w-full max-w-md">
            <div class="p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Създаване на нова папка</h3>

                <div class="mb-4">
                    <label for="folder-name-input" class="block text-sm font-medium text-gray-700 mb-2">
                        Име на папката:
                    </label>
                    <input type="text" id="folder-name-input"
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                           placeholder="Въведете име на папката">
                    <div id="folder-name-error" class="text-red-600 text-sm mt-1 hidden"></div>
                </div>

                <div class="flex justify-end space-x-3">
                    <button type="button" id="cancel-create-folder" class="px-4 py-2 bg-gray-200 text-gray-700 rounded hover:bg-gray-300 transition-colors">
                        Отказ
                    </button>
                    <button type="button" id="confirm-create-folder" class="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 transition-colors">
                        Създай
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
/* Image Manager Styles */
.image-item {
    position: relative;
    cursor: pointer;
    border-radius: 0.25rem;
    overflow: hidden;
    transition: all 0.2s ease;
}

.image-item.selected img {
    outline: 3px solid #8B5CF6 !important;
    outline-offset: -3px;
}

.image-item.selected:hover img {
    outline: 3px solid #7C3AED !important;
    outline-offset: -3px;
}

.directory-item {
    position: relative;
    cursor: pointer;
    border-radius: 0.25rem;
    overflow: hidden;
    border: 2px solid transparent;
    transition: all 0.2s ease;
}

.directory-item:hover {
    border-color: #d1d5db;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.breadcrumb-item {
    color: #2563eb;
    cursor: pointer;
    transition: color 0.15s ease;
    text-decoration: underline;
}

.breadcrumb-item:hover {
    color: #1d4ed8;
}

.breadcrumb-item.active {
    color: #8B5CF6;
    font-weight: 500;
    cursor: pointer;
    text-decoration: underline;
}

.breadcrumb-item.active:hover {
    color: #7c3aed;
}

.upload-progress-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem;
    background-color: #f9fafb;
    border-radius: 0.25rem;
}

.upload-progress-bar {
    flex: 1;
    background-color: #e5e7eb;
    border-radius: 9999px;
    height: 0.5rem;
    overflow: hidden;
}

.upload-progress-fill {
    height: 100%;
    background-color: #8B5CF6;
    transition: all 0.3s ease;
}

.upload-progress-text {
    font-size: 0.875rem;
    color: #4b5563;
    min-width: 60px;
    text-align: right;
}

/* Format warning styles */
.format-warning-icon {
    background-color: #f59e0b;
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    position: absolute;
    top: 4px;
    right: 4px;
    cursor: help;
    z-index: 10;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.format-warning-icon:hover {
    background-color: #d97706;
}

#upload-progress-modal {
    z-index: 5060;
}
#create-folder-modal {
    z-index: 5070;
}
</style>
