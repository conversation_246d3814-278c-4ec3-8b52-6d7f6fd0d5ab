<!DOCTYPE html>
<html lang="bg">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Тест на ImageManager</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .test-button { 
            background: #007bff; 
            color: white; 
            padding: 10px 20px; 
            border: none; 
            border-radius: 4px; 
            cursor: pointer; 
            margin: 10px; 
        }
        .test-button:hover { background: #0056b3; }
        .result { 
            margin: 10px 0; 
            padding: 10px; 
            border: 1px solid #ddd; 
            border-radius: 4px; 
            background: #f8f9fa; 
        }
    </style>
</head>
<body>
    <h1>Тест на ImageManager модула</h1>
    
    <div>
        <button class="test-button" onclick="testImageManagerLoad()">Тест 1: Проверка дали ImageManager се зарежда</button>
        <div id="test1-result" class="result"></div>
    </div>
    
    <div>
        <button class="test-button" onclick="testSingleSelection()">Тест 2: Single Selection</button>
        <div id="test2-result" class="result"></div>
    </div>
    
    <div>
        <button class="test-button" onclick="testMultiSelection()">Тест 3: Multi Selection</button>
        <div id="test3-result" class="result"></div>
    </div>
    
    <div>
        <button class="test-button" onclick="testModalOpen()">Тест 4: Отваряне на модал</button>
        <div id="test4-result" class="result"></div>
    </div>

    <!-- Симулираме BackendModule конфигурация -->
    <script>
        window.BackendModule = {
            config: {
                userToken: 'test-token-123'
            }
        };
    </script>

    <!-- Зареждаме ImageManager модула -->
    <script src="system/storage/theme/Backend/View/Javascript/image-manager.js"></script>

    <script>
        function testImageManagerLoad() {
            const result = document.getElementById('test1-result');
            if (typeof window.ImageManager !== 'undefined') {
                result.innerHTML = '<span style="color: green;">✓ ImageManager модулът е зареден успешно</span>';
                result.innerHTML += '<br>Налични методи: ' + Object.keys(window.ImageManager).join(', ');
            } else {
                result.innerHTML = '<span style="color: red;">✗ ImageManager модулът НЕ е зареден</span>';
            }
        }

        function testSingleSelection() {
            const result = document.getElementById('test2-result');
            try {
                if (typeof window.ImageManager === 'undefined') {
                    result.innerHTML = '<span style="color: red;">✗ ImageManager не е зареден</span>';
                    return;
                }

                window.ImageManager.open({
                    singleSelection: true,
                    onSelect: function(image) {
                        result.innerHTML = '<span style="color: green;">✓ Single selection callback извикан</span><br>Избрано изображение: ' + JSON.stringify(image);
                    }
                });
                
                result.innerHTML = '<span style="color: blue;">ℹ Single selection режим стартиран. Проверете дали се отваря модал.</span>';
            } catch (error) {
                result.innerHTML = '<span style="color: red;">✗ Грешка: ' + error.message + '</span>';
            }
        }

        function testMultiSelection() {
            const result = document.getElementById('test3-result');
            try {
                if (typeof window.ImageManager === 'undefined') {
                    result.innerHTML = '<span style="color: red;">✗ ImageManager не е зареден</span>';
                    return;
                }

                window.ImageManager.open({
                    singleSelection: false,
                    onSelect: function(images) {
                        result.innerHTML = '<span style="color: green;">✓ Multi selection callback извикан</span><br>Избрани изображения: ' + JSON.stringify(images);
                    }
                });
                
                result.innerHTML = '<span style="color: blue;">ℹ Multi selection режим стартиран. Проверете дали се отваря модал.</span>';
            } catch (error) {
                result.innerHTML = '<span style="color: red;">✗ Грешка: ' + error.message + '</span>';
            }
        }

        function testModalOpen() {
            const result = document.getElementById('test4-result');
            try {
                // Проверяваме дали модалът се отваря
                setTimeout(() => {
                    const modal = document.getElementById('image-manager-modal');
                    if (modal) {
                        const isVisible = modal.style.display === 'flex' || window.getComputedStyle(modal).display === 'flex';
                        if (isVisible) {
                            result.innerHTML = '<span style="color: green;">✓ Модалът е отворен и видим</span>';
                        } else {
                            result.innerHTML = '<span style="color: orange;">⚠ Модалът съществува, но не е видим</span>';
                        }
                    } else {
                        result.innerHTML = '<span style="color: red;">✗ Модалът не е намерен в DOM</span>';
                    }
                }, 1000);
                
                result.innerHTML = '<span style="color: blue;">ℹ Проверяваме състоянието на модала...</span>';
            } catch (error) {
                result.innerHTML = '<span style="color: red;">✗ Грешка: ' + error.message + '</span>';
            }
        }

        // Автоматично стартиране на първия тест при зареждане
        window.addEventListener('load', function() {
            testImageManagerLoad();
        });
    </script>
</body>
</html>
